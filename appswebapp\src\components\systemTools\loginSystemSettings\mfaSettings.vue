<template>
  <div class='mb-4'>
    <editSettingsHelper
      @cancel="cancel"
      @save="saveSettings"
      @changeMode="changeMode"
      title="Multi-Factor Authentication Settings"
      :isLoading="isUpdatingProcessed"
      :isDisabled="isDisabled"
      :isViewMode="isViewMode"
    >
      <div slot="settings-content">
        <ValidationObserver ref="validator">
          <detail-row fixedPayloadWidth>
            <span slot="title">OTP Code Length:</span>
            <div slot="payload">
              <ValidationProvider
                name="OTP Code Length"
                rules="required|min_value:4|max_value:10"
                v-slot="{ errors }"
              >
                <b-form-input
                  v-model.number="localSettings.otpCodeLength"
                  type="number"
                  min="4"
                  max="10"
                  class="login-settings-number-input"
                  :disabled="isViewMode"
                  :state="errors.length > 0 ? false : null"
                />
                <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
              </ValidationProvider>
              <small class="form-text text-muted">Must be between 4 and 10 characters</small>
            </div>
          </detail-row>

          <detail-row fixedPayloadWidth>
            <span slot="title">OTP Code TTL (minutes):</span>
            <div slot="payload">
              <ValidationProvider
                name="OTP Code TTL"
                rules="required|min_value:1|max_value:60"
                v-slot="{ errors }"
              >
                <b-form-input
                  v-model.number="localSettings.otpCodeTtlInMinutes"
                  type="number"
                  min="1"
                  max="60"
                  class="login-settings-number-input"
                  :disabled="isViewMode"
                  :state="errors.length > 0 ? false : null"
                />
                <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
              </ValidationProvider>
              <small class="form-text text-muted">Must be between 1 and 60 minutes</small>
            </div>
          </detail-row>

          <detail-row fixedPayloadWidth>
            <span slot="title">Verification Attempts Limit:</span>
            <div slot="payload">
              <ValidationProvider
                name="Verification Attempts Limit"
                rules="required|min_value:1|max_value:10"
                v-slot="{ errors }"
              >
                <b-form-input
                  v-model.number="localSettings.otpCodeVerificationAttemptsLimit"
                  type="number"
                  min="1"
                  max="10"
                  class="login-settings-number-input"
                  :disabled="isViewMode"
                  :state="errors.length > 0 ? false : null"
                />
                <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
              </ValidationProvider>
              <small class="form-text text-muted">Must be between 1 and 10 attempts</small>
            </div>
          </detail-row>

          <detail-row fixedPayloadWidth>
            <span slot="title">Resend Interval (seconds):</span>
            <div slot="payload">
              <ValidationProvider
                name="Resend Interval"
                rules="required|min_value:30|max_value:300"
                v-slot="{ errors }"
              >
                <b-form-input
                  v-model.number="localSettings.otpCodeResendIntervalInSeconds"
                  type="number"
                  min="30"
                  max="300"
                  class="login-settings-number-input"
                  :disabled="isViewMode"
                  :state="errors.length > 0 ? false : null"
                />
                <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
              </ValidationProvider>
              <small class="form-text text-muted">Must be between 30 and 300 seconds</small>
            </div>
          </detail-row>
        </ValidationObserver>
      </div>
    </editSettingsHelper>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import { ValidationObserver, ValidationProvider } from 'vee-validate'

export default {
  name: 'mfa-settings',
  props: {
    settings: {
      type: Object,
      required: true
    },
    isUpdatingProcessed: {
      type: Boolean,
      default: false
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isViewMode: true,
      localSettings: {
        otpCodeLength: 6,
        otpCodeTtlInMinutes: 5,
        otpCodeVerificationAttemptsLimit: 5,
        otpCodeResendIntervalInSeconds: 60
      }
    }
  },
  created () {
    this.initData()
  },
  watch: {
    settings: {
      handler () {
        this.initData()
      },
      deep: true
    }
  },
  components: {
    'detail-row': detailRow,
    'editSettingsHelper': editSettingsHelper,
    ValidationObserver,
    ValidationProvider
  },
  methods: {
    initData () {
      if (this.settings) {
        this.localSettings = {
          otpCodeLength: this.settings.otpCodeLength,
          otpCodeTtlInMinutes: this.settings.otpCodeTtlInMinutes,
          otpCodeVerificationAttemptsLimit: this.settings.otpCodeVerificationAttemptsLimit,
          otpCodeResendIntervalInSeconds: this.settings.otpCodeResendIntervalInSeconds
        }
      }
    },

    async saveSettings () {
      const isValid = await this.$refs.validator.validate()
      if (isValid) {
        this.$emit('save', this.localSettings)
        this.isViewMode = true
      }
    },

    changeMode (mode) {
      this.isViewMode = mode
    },

    cancel () {
      this.initData()
      this.changeMode(true)
      this.$refs.validator.reset()
    }
  }
}
</script>

<style>
.login-settings-number-input {
  width: 300px;
}
</style>
