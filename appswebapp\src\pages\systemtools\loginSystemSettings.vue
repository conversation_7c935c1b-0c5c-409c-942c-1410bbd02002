<template>
  <div>
    <h4>Login System Settings</h4>
    <div v-if="!isLoading && !isError">
      <b-card>
        <!-- MFA Settings Section -->
        <mfa-settings
          :settings="settings.mfa"
          :isUpdatingProcessed="sections.mfa.isUpdatingProcessed"
          :isDisabled="sections.mfa.isDisabled"
          @save="saveMfaSettings"
        />

        <!-- Email Settings Section -->
        <email-settings
          :settings="settings.email"
          :isUpdatingProcessed="sections.email.isUpdatingProcessed"
          :isDisabled="sections.email.isDisabled"
          @save="saveEmailSettings"
        />

        <!-- SMS Settings Section -->
        <sms-settings
          :settings="settings.sms"
          :isUpdatingProcessed="sections.sms.isUpdatingProcessed"
          :isDisabled="sections.sms.isDisabled"
          @save="saveSmsSettings"
        />
      </b-card>
    </div>
    <div v-else-if="isError" class="text-center mb-3">
      <b-alert variant="danger" show>
        <h5>Error Loading Settings</h5>
        <p>Unable to load login system settings. Please try again later.</p>
        <b-button variant="outline-danger" @click="loadSettings">
          <i class="ion ion-md-refresh mr-1"></i>
          Retry
        </b-button>
      </b-alert>
    </div>
    <div v-else class="my-5 text-center">
      <b-spinner variant="primary"></b-spinner>
      <p class="mt-2">Loading login system settings...</p>
    </div>
  </div>
</template>

<script>
import LoginSystemSettingsService from '@/services/LoginSystemSettingsService'
import loader from '@/components/_shared/loader'
import mfaSettings from '@/components/systemtools/loginSystemSettings/mfaSettings'
import emailSettings from '@/components/systemtools/loginSystemSettings/emailSettings'
import smsSettings from '@/components/systemtools/loginSystemSettings/smsSettings'

export default {
  name: 'login-system-settings',
  metaInfo: {
    title: 'Login System Settings'
  },
  data () {
    return {
      isLoading: true,
      isError: false,
      settings: {
        id: '',
        mfa: {
          otpCodeLength: 6,
          otpCodeTtlInMinutes: 5,
          otpCodeVerificationAttemptsLimit: 5,
          otpCodeResendIntervalInSeconds: 60
        },
        email: {
          from: '',
          otpSubject: '',
          otpBodyTemplate: ''
        },
        sms: {
          from: '',
          otpBodyTemplate: ''
        },
        updatedAtUtc: null
      },
      sections: {
        mfa: { isDisabled: false, isUpdatingProcessed: false },
        email: { isDisabled: false, isUpdatingProcessed: false },
        sms: { isDisabled: false, isUpdatingProcessed: false }
      }
    }
  },
  created () {
    this.loadSettings()
  },
  components: {
    'loader': loader,
    'mfa-settings': mfaSettings,
    'email-settings': emailSettings,
    'sms-settings': smsSettings
  },
  methods: {
    async loadSettings () {
      this.isLoading = true
      this.isError = false

      try {
        const response = await LoginSystemSettingsService.getSettings()

        if (response.data) {
          const backendData = response.data

          this.settings = {
            id: backendData.id || '',
            mfa: backendData.mfa || {},
            email: backendData.email || {},
            sms: backendData.sms || {},
            updatedAtUtc: backendData.updatedAtUtc || null
          }
        }
      } catch (ex) {
        this.isError = true
        this.$toaster.error('Cannot load login system settings')
        this.$logger.handleError(ex, 'Cannot load login system settings')
      } finally {
        this.isLoading = false
      }
    },

    async saveMfaSettings (mfaSettings) {
      const updatedSettings = { ...this.settings, mfa: mfaSettings }
      await this.saveSettings('mfa', updatedSettings)
    },

    async saveEmailSettings (emailSettings) {
      const updatedSettings = { ...this.settings, email: emailSettings }
      await this.saveSettings('email', updatedSettings)
    },

    async saveSmsSettings (smsSettings) {
      const updatedSettings = { ...this.settings, sms: smsSettings }
      await this.saveSettings('sms', updatedSettings)
    },

    async saveSettings (sectionName, updatedSettings) {
      try {
        this.sections[sectionName].isUpdatingProcessed = true
        this.makeOtherSectionsDisabled(sectionName)

        const response = await LoginSystemSettingsService.updateSettings(updatedSettings)
        if (response.data) {
          this.settings = response.data
          this.$toaster.success(`${this.getSectionDisplayName(sectionName)} settings saved successfully`)
        }
      } catch (ex) {
        const errorMessage = (ex.response && ex.response.data) || ex.message || 'Unknown error'
        this.$toaster.error(`Cannot update ${this.getSectionDisplayName(sectionName)} settings. ${errorMessage}`)
        this.$logger.handleError(ex, `Cannot update ${sectionName} settings`)
      } finally {
        this.sections[sectionName].isUpdatingProcessed = false
        this.makeAllSectionsEnabled()
      }
    },

    makeOtherSectionsDisabled (currentSection) {
      Object.keys(this.sections).forEach(section => {
        if (section !== currentSection) {
          this.sections[section].isDisabled = true
        }
      })
    },

    makeAllSectionsEnabled () {
      Object.keys(this.sections).forEach(section => {
        this.sections[section].isDisabled = false
      })
    },

    getSectionDisplayName (sectionName) {
      const displayNames = {
        mfa: 'MFA',
        email: 'Email',
        sms: 'SMS'
      }
      return displayNames[sectionName] || sectionName
    }
  }
}
</script>
