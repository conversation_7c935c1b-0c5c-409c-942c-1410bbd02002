﻿using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace EBizAutos.Apps.Authentication.CommonLib.Models {
	public class SmsSettings {
		[RegularExpression(@"^\d{10,11}$", ErrorMessage = "Phone number must contain between 10 and 11 digits only.")]
		public string From { get; set; }

		[Required(ErrorMessage = "OTP SMS message template is required.")]
		public string OtpBodyTemplate { get; set; }
	}
}