<template>
  <div class='mb-4'>
    <editSettingsHelper
      @cancel="cancel"
      @save="saveSettings"
      @changeMode="changeMode"
      title="SMS Settings"
      :isLoading="isUpdatingProcessed"
      :isDisabled="isDisabled"
      :isViewMode="isViewMode"
    >
      <div slot="settings-content">
        <ValidationObserver ref="validator">
          <detail-row fixedPayloadWidth>
            <span slot="title">From Phone Number:</span>
            <div slot="payload">
              <ValidationProvider
                name="From Phone Number"
                rules="sms_phone"
                v-slot="{ errors }"
              >
                <b-form-input
                  v-model="localSettings.from"
                  type="text"
                  placeholder="**********"
                  maxlength="11"
                  class="login-settings-phone-input"
                  :disabled="isViewMode"
                  :state="errors.length > 0 ? false : null"
                />
                <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
              </ValidationProvider>
              <small class="form-text text-muted">Phone number used as sender for OTP SMS (10-11 digits only)</small>
            </div>
          </detail-row>

          <detail-row fixedPayloadWidth>
            <span slot="title">OTP SMS Template:</span>
            <div slot="payload">
              <ValidationProvider
                name="OTP SMS Template"
                rules="required"
                v-slot="{ errors }"
              >
                <b-form-textarea
                  v-model="localSettings.otpBodyTemplate"
                  rows="4"
                  placeholder="Your eBizAutos One-Time Passcode is {0}. It will expire in {1} minutes. DO NOT share it with anyone. We will never ask for the code."
                  :disabled="isViewMode"
                  :state="errors.length > 0 ? false : null"
                />
                <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
              </ValidationProvider>
              <small class="form-text text-muted">
                SMS message template for OTP. Use {0} for the OTP code and {1} for expiration time in minutes.
              </small>
            </div>
          </detail-row>
        </ValidationObserver>
      </div>
    </editSettingsHelper>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import { ValidationObserver, ValidationProvider } from 'vee-validate'

export default {
  name: 'sms-settings',
  props: {
    settings: {
      type: Object,
      required: true
    },
    isUpdatingProcessed: {
      type: Boolean,
      default: false
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isViewMode: true,
      localSettings: {
        from: '',
        otpBodyTemplate: ''
      }
    }
  },
  created () {
    this.initData()
  },
  watch: {
    settings: {
      handler () {
        this.initData()
      },
      deep: true
    }
  },
  components: {
    'detail-row': detailRow,
    'editSettingsHelper': editSettingsHelper,
    ValidationObserver,
    ValidationProvider
  },
  methods: {
    initData () {
      if (this.settings) {
        this.localSettings = {
          from: this.settings.from,
          otpBodyTemplate: this.settings.otpBodyTemplate
        }
      }
    },

    async saveSettings () {
      const isValid = await this.$refs.validator.validate()
      if (isValid) {
        this.$emit('save', this.localSettings)
        this.isViewMode = true
      }
    },

    changeMode (mode) {
      this.isViewMode = mode
    },

    cancel () {
      this.initData()
      this.changeMode(true)
      this.$refs.validator.reset()
    }
  }
}
</script>

<style>
.login-settings-phone-input {
  width: 300px;
}
</style>
