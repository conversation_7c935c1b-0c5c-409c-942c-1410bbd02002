{"Version": 1, "WorkspaceRootPath": "D:\\Work\\ebizautos\\_solutions\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{1AB47BA0-C486-469C-8708-C4A4DBB60E51}|..\\src\\EBizAutos.Apps.Shared.Api\\EBizAutos.Apps.Shared.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.shared.api\\utilities\\managers\\mfachallengemanager.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{1AB47BA0-C486-469C-8708-C4A4DBB60E51}|..\\src\\EBizAutos.Apps.Shared.Api\\EBizAutos.Apps.Shared.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.shared.api\\controllers\\mfacontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{1AB47BA0-C486-469C-8708-C4A4DBB60E51}|..\\src\\EBizAutos.Apps.Shared.Api\\EBizAutos.Apps.Shared.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.shared.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{DA9E41A0-90C9-40DB-87BA-166B654BDBAF}|..\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.commonlib\\enums\\authenticationenums.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{1AB47BA0-C486-469C-8708-C4A4DBB60E51}|..\\src\\EBizAutos.Apps.Shared.Api\\EBizAutos.Apps.Shared.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.shared.api\\appsettings.staging.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{1AB47BA0-C486-469C-8708-C4A4DBB60E51}|..\\src\\EBizAutos.Apps.Shared.Api\\EBizAutos.Apps.Shared.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.shared.api\\appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{1AB47BA0-C486-469C-8708-C4A4DBB60E51}|..\\src\\EBizAutos.Apps.Shared.Api\\EBizAutos.Apps.Shared.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.shared.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{1AB47BA0-C486-469C-8708-C4A4DBB60E51}|..\\src\\EBizAutos.Apps.Shared.Api\\EBizAutos.Apps.Shared.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.shared.api\\appconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{1AB47BA0-C486-469C-8708-C4A4DBB60E51}|..\\src\\EBizAutos.Apps.Shared.Api\\EBizAutos.Apps.Shared.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.shared.api\\utilities\\extensions\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{DA9E41A0-90C9-40DB-87BA-166B654BDBAF}|..\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.commonlib\\ebizautos.apps.commonlib.csproj||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{1AB47BA0-C486-469C-8708-C4A4DBB60E51}|..\\src\\EBizAutos.Apps.Shared.Api\\EBizAutos.Apps.Shared.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.shared.api\\utilities\\managers\\userconfigurationmanager.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{1AB47BA0-C486-469C-8708-C4A4DBB60E51}|..\\src\\EBizAutos.Apps.Shared.Api\\EBizAutos.Apps.Shared.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.shared.api\\utilities\\managers\\userannouncementmanager.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{1AB47BA0-C486-469C-8708-C4A4DBB60E51}|..\\src\\EBizAutos.Apps.Shared.Api\\EBizAutos.Apps.Shared.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.shared.api\\utilities\\managers\\iapimanager.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FAFE79BC-7B97-403B-92BE-C679A17F9718}|..\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\EBizAutos.Apps.Authentication.MongoDbRepository.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.authentication.mongodbrepository\\internalconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{85684AED-DBF5-4076-A04D-82EE3973C70B}|..\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.authentication.commonlib\\models\\mfachallengemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{85684AED-DBF5-4076-A04D-82EE3973C70B}|..\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.authentication.commonlib\\models\\role.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{85684AED-DBF5-4076-A04D-82EE3973C70B}|..\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.authentication.commonlib\\models\\jwtauthenticationsettings.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{85684AED-DBF5-4076-A04D-82EE3973C70B}|..\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.authentication.commonlib\\models\\cookieauthenticationsettings.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{85684AED-DBF5-4076-A04D-82EE3973C70B}|..\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.authentication.commonlib\\models\\basicauthenticationidentity.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{85684AED-DBF5-4076-A04D-82EE3973C70B}|..\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.authentication.commonlib\\models\\authenticationsettings.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FAFE79BC-7B97-403B-92BE-C679A17F9718}|..\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\EBizAutos.Apps.Authentication.MongoDbRepository.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.authentication.mongodbrepository\\cpusermongodbrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{74987A14-3434-4D10-9322-18DE262BCE94}|..\\src\\EBizAutos.Apps.AccountManagement.Api\\EBizAutos.Apps.AccountManagement.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.accountmanagement.api\\utilities\\managers\\appscontactmanager.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{ABB9A914-E163-4DA6-A997-5CBB19EA0B7A}|..\\src\\EBizAutos.Apps.UsersManagement.Api\\EBizAutos.Apps.UsersManagement.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.usersmanagement.api\\managers\\usermanager.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{ABB9A914-E163-4DA6-A997-5CBB19EA0B7A}|..\\src\\EBizAutos.Apps.UsersManagement.Api\\EBizAutos.Apps.UsersManagement.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.usersmanagement.api\\extensions\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{ABB9A914-E163-4DA6-A997-5CBB19EA0B7A}|..\\src\\EBizAutos.Apps.UsersManagement.Api\\EBizAutos.Apps.UsersManagement.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.usersmanagement.api\\appsettings.staging.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{ABB9A914-E163-4DA6-A997-5CBB19EA0B7A}|..\\src\\EBizAutos.Apps.UsersManagement.Api\\EBizAutos.Apps.UsersManagement.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.usersmanagement.api\\appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{ABB9A914-E163-4DA6-A997-5CBB19EA0B7A}|..\\src\\EBizAutos.Apps.UsersManagement.Api\\EBizAutos.Apps.UsersManagement.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.usersmanagement.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{ABB9A914-E163-4DA6-A997-5CBB19EA0B7A}|..\\src\\EBizAutos.Apps.UsersManagement.Api\\EBizAutos.Apps.UsersManagement.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.usersmanagement.api\\appconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{74987A14-3434-4D10-9322-18DE262BCE94}|..\\src\\EBizAutos.Apps.AccountManagement.Api\\EBizAutos.Apps.AccountManagement.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.accountmanagement.api\\utilities\\extensions\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{74987A14-3434-4D10-9322-18DE262BCE94}|..\\src\\EBizAutos.Apps.AccountManagement.Api\\EBizAutos.Apps.AccountManagement.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.accountmanagement.api\\servicebus\\consumers\\testeventsconsumer.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{51510B59-ABF1-434B-8FA8-BAC00E0EB19C}|..\\src\\EBizAutos.Apps.Leads.CommonLib\\EBizAutos.Apps.Leads.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.leads.commonlib\\utilities\\ebiztwilioclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{51510B59-ABF1-434B-8FA8-BAC00E0EB19C}|..\\src\\EBizAutos.Apps.Leads.CommonLib\\EBizAutos.Apps.Leads.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.leads.commonlib\\configuration\\twiliosettings.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{51510B59-ABF1-434B-8FA8-BAC00E0EB19C}|..\\src\\EBizAutos.Apps.Leads.CommonLib\\EBizAutos.Apps.Leads.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.leads.commonlib\\models\\twilio\\twiliomessage.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{1AB47BA0-C486-469C-8708-C4A4DBB60E51}|..\\src\\EBizAutos.Apps.Shared.Api\\EBizAutos.Apps.Shared.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.shared.api\\models\\mfa\\mfastartmethodsetupresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{1AB47BA0-C486-469C-8708-C4A4DBB60E51}|..\\src\\EBizAutos.Apps.Shared.Api\\EBizAutos.Apps.Shared.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.shared.api\\models\\mfa\\mfastartmethodsetuprequestmodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{1AB47BA0-C486-469C-8708-C4A4DBB60E51}|..\\src\\EBizAutos.Apps.Shared.Api\\EBizAutos.Apps.Shared.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.shared.api\\controllers\\loginsystemsettingscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FAFE79BC-7B97-403B-92BE-C679A17F9718}|..\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\EBizAutos.Apps.Authentication.MongoDbRepository.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.authentication.mongodbrepository\\loginsystemsettingsmongodbrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{85684AED-DBF5-4076-A04D-82EE3973C70B}|..\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.authentication.commonlib\\models\\smssettings.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{85684AED-DBF5-4076-A04D-82EE3973C70B}|..\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.authentication.commonlib\\models\\mfasettings.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{85684AED-DBF5-4076-A04D-82EE3973C70B}|..\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.authentication.commonlib\\models\\loginsystemsettings.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{85684AED-DBF5-4076-A04D-82EE3973C70B}|..\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.authentication.commonlib\\models\\emailsettings.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{85684AED-DBF5-4076-A04D-82EE3973C70B}|..\\src\\EBizAutos.Apps.Authentication.CommonLib\\EBizAutos.Apps.Authentication.CommonLib.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.authentication.commonlib\\abstract\\repositories\\iloginsystemsettingsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{ABB9A914-E163-4DA6-A997-5CBB19EA0B7A}|..\\src\\EBizAutos.Apps.UsersManagement.Api\\EBizAutos.Apps.UsersManagement.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.usersmanagement.api\\servicebus\\handlers\\ieventhandler.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{ABB9A914-E163-4DA6-A997-5CBB19EA0B7A}|..\\src\\EBizAutos.Apps.UsersManagement.Api\\EBizAutos.Apps.UsersManagement.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.usersmanagement.api\\servicebus\\handlers\\contactupdatedeventhandler.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{ABB9A914-E163-4DA6-A997-5CBB19EA0B7A}|..\\src\\EBizAutos.Apps.UsersManagement.Api\\EBizAutos.Apps.UsersManagement.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.usersmanagement.api\\servicebus\\events\\userupdatedevent.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{ABB9A914-E163-4DA6-A997-5CBB19EA0B7A}|..\\src\\EBizAutos.Apps.UsersManagement.Api\\EBizAutos.Apps.UsersManagement.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.usersmanagement.api\\servicebus\\consumers\\contactupdatedeventconsumer.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{ABB9A914-E163-4DA6-A997-5CBB19EA0B7A}|..\\src\\EBizAutos.Apps.UsersManagement.Api\\EBizAutos.Apps.UsersManagement.Api.csproj|d:\\work\\ebizautos\\src\\ebizautos.apps.usersmanagement.api\\servicebus\\consumers\\baseeventconsumer.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 11, "Children": [{"$type": "Bookmark", "Name": "ST:131:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:130:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:135:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 3, "Title": "AuthenticationEnums.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\Enums\\AuthenticationEnums.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.CommonLib\\Enums\\AuthenticationEnums.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\Enums\\AuthenticationEnums.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.CommonLib\\Enums\\AuthenticationEnums.cs", "ViewState": "AgIAABAAAAAAAAAAAAAewB8AAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T19:30:46.481Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "ServiceCollectionExtensions.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Extensions\\ServiceCollectionExtensions.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Extensions\\ServiceCollectionExtensions.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Extensions\\ServiceCollectionExtensions.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Extensions\\ServiceCollectionExtensions.cs", "ViewState": "AgIAAKUAAAAAAAAAAAAuwL0AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:58:35.413Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "MfaChallengeManager.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\MfaChallengeManager.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\MfaChallengeManager.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\MfaChallengeManager.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\MfaChallengeManager.cs", "ViewState": "AgIAAGIAAAAAAAAAAAAIwMMAAABMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-03T16:16:09.665Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "AppConfiguration.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\AppConfiguration.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Shared.Api\\AppConfiguration.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\AppConfiguration.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Shared.Api\\AppConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAD0AAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:59:47.302Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\appsettings.Development.json", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Shared.Api\\appsettings.Development.json", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\appsettings.Development.json", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Shared.Api\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAG8AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-09-04T17:01:31.921Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "appsettings.Production.json", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\appsettings.Production.json", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Shared.Api\\appsettings.Production.json", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\appsettings.Production.json", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Shared.Api\\appsettings.Production.json", "ViewState": "AgIAAFQAAAAAAAAAAAAowH4AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-09-04T17:01:49.936Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "AuthController.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Shared.Api\\Controllers\\AuthController.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Controllers\\AuthController.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Shared.Api\\Controllers\\AuthController.cs", "ViewState": "AgIAAFgAAAAAAAAAAAASwGsAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T10:02:12.755Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "MfaController.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Controllers\\MfaController.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Shared.Api\\Controllers\\MfaController.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Controllers\\MfaController.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Shared.Api\\Controllers\\MfaController.cs", "ViewState": "AgIAAGAAAAAAAAAAAAAjwHIAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T10:02:25.643Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "MfaStartMethodSetupResponseModel.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Models\\Mfa\\MfaStartMethodSetupResponseModel.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Shared.Api\\Models\\Mfa\\MfaStartMethodSetupResponseModel.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Models\\Mfa\\MfaStartMethodSetupResponseModel.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Shared.Api\\Models\\Mfa\\MfaStartMethodSetupResponseModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T19:19:22.906Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "appsettings.Staging.json", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\appsettings.Staging.json", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Shared.Api\\appsettings.Staging.json", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\appsettings.Staging.json", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Shared.Api\\appsettings.Staging.json", "ViewState": "AgIAAFEAAAAAAAAAAAAowHAAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-09-04T17:02:13.066Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "MfaStartMethodSetupRequestModel.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Models\\Mfa\\MfaStartMethodSetupRequestModel.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Shared.Api\\Models\\Mfa\\MfaStartMethodSetupRequestModel.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Models\\Mfa\\MfaStartMethodSetupRequestModel.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Shared.Api\\Models\\Mfa\\MfaStartMethodSetupRequestModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T19:16:10.548Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "EBizAutos.Apps.CommonLib.csproj", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj", "RelativeToolTip": "..\\src\\EBizAutos.Apps.CommonLib\\EBizAutos.Apps.CommonLib.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAABgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-09-04T17:33:49.368Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "UserConfigurationManager.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\UserConfigurationManager.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\UserConfigurationManager.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\UserConfigurationManager.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\UserConfigurationManager.cs", "ViewState": "AgIAAKIAAAAAAAAAAAAmwKgAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:59:31.382Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "UserAnnouncementManager.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\UserAnnouncementManager.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\UserAnnouncementManager.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\UserAnnouncementManager.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\UserAnnouncementManager.cs", "ViewState": "AgIAAFAAAAAAAAAAAAAIwFYAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:59:26.068Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "IApiManager.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\IApiManager.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\IApiManager.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\IApiManager.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Shared.Api\\Utilities\\Managers\\IApiManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:58:54.165Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "LoginSystemSettingsController.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Controllers\\LoginSystemSettingsController.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Shared.Api\\Controllers\\LoginSystemSettingsController.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Shared.Api\\Controllers\\LoginSystemSettingsController.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Shared.Api\\Controllers\\LoginSystemSettingsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADEAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:57:42.467Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "LoginSystemSettingsMongoDbRepository.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\LoginSystemSettingsMongoDbRepository.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\LoginSystemSettingsMongoDbRepository.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\LoginSystemSettingsMongoDbRepository.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\LoginSystemSettingsMongoDbRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAEAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:56:22.455Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "InternalConstants.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\InternalConstants.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\InternalConstants.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\InternalConstants.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\InternalConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:55:57.649Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "SmsSettings.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\SmsSettings.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\SmsSettings.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\SmsSettings.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\SmsSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:55:25.12Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "MfaSettings.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\MfaSettings.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\MfaSettings.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\MfaSettings.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\MfaSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:54:54.52Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "LoginSystemSettings.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\LoginSystemSettings.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\LoginSystemSettings.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\LoginSystemSettings.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\LoginSystemSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:54:09.164Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "Role.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\Role.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\Role.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\Role.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\Role.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:53:50.986Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "JwtAuthenticationSettings.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\JwtAuthenticationSettings.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\JwtAuthenticationSettings.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\JwtAuthenticationSettings.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\JwtAuthenticationSettings.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAowB0AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:53:44.518Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "MfaChallengeModel.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\MfaChallengeModel.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\MfaChallengeModel.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\MfaChallengeModel.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\MfaChallengeModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:53:43.407Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "EmailSettings.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\EmailSettings.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\EmailSettings.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\EmailSettings.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\EmailSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:53:16.825Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "CookieAuthenticationSettings.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\CookieAuthenticationSettings.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\CookieAuthenticationSettings.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\CookieAuthenticationSettings.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\CookieAuthenticationSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:52:56.925Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "BasicAuthenticationIdentity.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\BasicAuthenticationIdentity.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\BasicAuthenticationIdentity.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\BasicAuthenticationIdentity.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\BasicAuthenticationIdentity.cs", "ViewState": "AgIAABoAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:52:54.007Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "AuthenticationSettings.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\AuthenticationSettings.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\AuthenticationSettings.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\AuthenticationSettings.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Models\\AuthenticationSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:52:48.525Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "ILoginSystemSettingsRepository.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Abstract\\Repositories\\ILoginSystemSettingsRepository.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Abstract\\Repositories\\ILoginSystemSettingsRepository.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.CommonLib\\Abstract\\Repositories\\ILoginSystemSettingsRepository.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Authentication.CommonLib\\Abstract\\Repositories\\ILoginSystemSettingsRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:52:17.461Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "appsettings.Staging.json", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\appsettings.Staging.json", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\appsettings.Staging.json", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\appsettings.Staging.json", "RelativeToolTip": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\appsettings.Staging.json", "ViewState": "AgIAABwAAAAAAAAAAAAswEUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-09-04T16:29:42.27Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "appsettings.Production.json", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\appsettings.Production.json", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\appsettings.Production.json", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\appsettings.Production.json", "RelativeToolTip": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\appsettings.Production.json", "ViewState": "AgIAADAAAAAAAAAAAAAswEoAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-09-04T16:29:34.826Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\appsettings.Development.json", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\appsettings.Development.json", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\appsettings.Development.json", "RelativeToolTip": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\appsettings.Development.json", "ViewState": "AgIAAB0AAAAAAAAAAAAswEYAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-09-04T16:29:13.892Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "AppsContactManager.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.AccountManagement.Api\\Utilities\\Managers\\AppsContactManager.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.AccountManagement.Api\\Utilities\\Managers\\AppsContactManager.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.AccountManagement.Api\\Utilities\\Managers\\AppsContactManager.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.AccountManagement.Api\\Utilities\\Managers\\AppsContactManager.cs", "ViewState": "AgIAADoAAAAAAAAAAAAgwFAAAABfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T14:53:34.782Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "UserManager.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\Managers\\UserManager.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\Managers\\UserManager.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\Managers\\UserManager.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\Managers\\UserManager.cs", "ViewState": "AgIAABMCAAAAAAAAAAAhwCoCAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T15:01:28.769Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "CPUserMongoDbRepository.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\CPUserMongoDbRepository.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\CPUserMongoDbRepository.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\CPUserMongoDbRepository.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Authentication.MongoDbRepository\\CPUserMongoDbRepository.cs", "ViewState": "AgIAAPYBAAAAAAAAAAApwAsCAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T14:56:36.029Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "AppConfiguration.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\AppConfiguration.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\AppConfiguration.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\AppConfiguration.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\AppConfiguration.cs", "ViewState": "AgIAACUAAAAAAAAAAAAmwDsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:27:08.668Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "IEventHandler.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Handlers\\IEventHandler.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Handlers\\IEventHandler.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Handlers\\IEventHandler.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Handlers\\IEventHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:26:42.839Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "ContactUpdatedEventHandler.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Handlers\\ContactUpdatedEventHandler.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Handlers\\ContactUpdatedEventHandler.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Handlers\\ContactUpdatedEventHandler.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Handlers\\ContactUpdatedEventHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T16:26:05.697Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "ServiceCollectionExtensions.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\Extensions\\ServiceCollectionExtensions.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\Extensions\\ServiceCollectionExtensions.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\Extensions\\ServiceCollectionExtensions.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\Extensions\\ServiceCollectionExtensions.cs", "ViewState": "AgIAABIAAAAAAAAAAAAAACIAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T15:00:54.4Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "UserUpdatedEvent.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Events\\UserUpdatedEvent.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Events\\UserUpdatedEvent.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Events\\UserUpdatedEvent.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Events\\UserUpdatedEvent.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T15:07:54.711Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "ContactUpdatedEventConsumer.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Consumers\\ContactUpdatedEventConsumer.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Consumers\\ContactUpdatedEventConsumer.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Consumers\\ContactUpdatedEventConsumer.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Consumers\\ContactUpdatedEventConsumer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T15:07:02.834Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "BaseEventConsumer.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Consumers\\BaseEventConsumer.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Consumers\\BaseEventConsumer.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Consumers\\BaseEventConsumer.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.UsersManagement.Api\\ServiceBus\\Consumers\\BaseEventConsumer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB8AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T15:06:05.527Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "ServiceCollectionExtensions.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.AccountManagement.Api\\Utilities\\Extensions\\ServiceCollectionExtensions.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.AccountManagement.Api\\Utilities\\Extensions\\ServiceCollectionExtensions.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.AccountManagement.Api\\Utilities\\Extensions\\ServiceCollectionExtensions.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.AccountManagement.Api\\Utilities\\Extensions\\ServiceCollectionExtensions.cs", "ViewState": "AgIAABYBAAAAAAAAAAAIwEAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T14:51:58.202Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "TestEventsConsumer.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.AccountManagement.Api\\ServiceBus\\Consumers\\TestEventsConsumer.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.AccountManagement.Api\\ServiceBus\\Consumers\\TestEventsConsumer.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.AccountManagement.Api\\ServiceBus\\Consumers\\TestEventsConsumer.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.AccountManagement.Api\\ServiceBus\\Consumers\\TestEventsConsumer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-04T14:49:14.42Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "EBizTwilioClient.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Leads.CommonLib\\Utilities\\EBizTwilioClient.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Leads.CommonLib\\Utilities\\EBizTwilioClient.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Leads.CommonLib\\Utilities\\EBizTwilioClient.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Leads.CommonLib\\Utilities\\EBizTwilioClient.cs", "ViewState": "AgIAAEcCAAAAAAAAAAAUwGgCAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-03T13:20:22.638Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "TwilioSettings.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Leads.CommonLib\\Configuration\\TwilioSettings.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Leads.CommonLib\\Configuration\\TwilioSettings.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Leads.CommonLib\\Configuration\\TwilioSettings.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Leads.CommonLib\\Configuration\\TwilioSettings.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAowBcAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-03T13:20:04.62Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "TwilioMessage.cs", "DocumentMoniker": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Leads.CommonLib\\Models\\Twilio\\TwilioMessage.cs", "RelativeDocumentMoniker": "..\\src\\EBizAutos.Apps.Leads.CommonLib\\Models\\Twilio\\TwilioMessage.cs", "ToolTip": "D:\\Work\\ebizautos\\src\\EBizAutos.Apps.Leads.CommonLib\\Models\\Twilio\\TwilioMessage.cs", "RelativeToolTip": "..\\src\\EBizAutos.Apps.Leads.CommonLib\\Models\\Twilio\\TwilioMessage.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAkwD4AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-03T12:39:44.229Z"}]}]}]}