import Vue from 'vue'
import { ValidationProvider, extend, ValidationObserver } from 'vee-validate'
import * as rules from 'vee-validate/dist/rules'
import { messages } from 'vee-validate/dist/locale/en.json'
import XmlValidator from './xmlValidator'
import isUrl from './urlValidator'

export default function setup () {
  // add all available rules
  Object.keys(rules).forEach(rule => {
    extend(rule, {
      ...rules[rule], // copies rule configuration
      message: messages[rule] // assign message
    })
  })

  // custom phone validator
  extend('phone', {
    message (fieldName) {
      return `${fieldName} is not a valid phone number`
    },
    validate (value) {
      return new Promise(resolve => {
        value = value || ''
        let regex = /^[(]?[0-9]{3}[)]?[-\s]?[0-9]{3}[-\s]?[0-9]{4,6}$/im
        let phone = value.replaceAll(/\D/g, '')
        resolve({ valid: regex.test(value) && phone.length === 10 })
      })
    }
  })

  // custom SMS phone number validator (digits only, 10-11 digits)
  extend('sms_phone', {
    message (fieldName) {
      return `${fieldName} must contain between 10 and 11 digits only`
    },
    validate (value) {
      if (!value) return true // Allow empty values (handled by required rule)
      // Check if value contains only digits and is between 10-11 digits
      const digitsOnly = /^\d{10,11}$/.test(value)
      return digitsOnly
    }
  })

  // custom url validator
  extend('url', {
    message (fieldName) {
      return `${fieldName} is not valid url`
    },
    validate (value) {
      return isUrl(value)
    }
  })

  extend('password', {
    params: ['target'],
    validate (value, { target }) {
      return value === target
    },
    message: 'Password confirmation does not match'
  })

  extend('xml', value => {
    return XmlValidator.validatePossibleXmlText(value)
  })

  extend('encodeXml', value => {
    return XmlValidator.validateEncodedXmlText(value)
  })

  extend('min_args', {
    validate (value, args) {
      return value && value.length >= args.length
    },
    params: ['length'],
    message: 'The {_field_} field must have at least {length} elements'
  })

  extend('max_args', {
    validate (value, args) {
      return value && value.length <= args.length
    },
    params: ['length'],
    message: 'The {_field_} field must have no more than {length} elements'
  })

  Vue.component('ValidationProvider', ValidationProvider)
  Vue.component('ValidationObserver', ValidationObserver)
}
